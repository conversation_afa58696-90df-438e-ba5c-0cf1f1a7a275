class JackpotTestTool {
    constructor() {
        this.ws = null;
        this.flagIdx = 0;
        this.itemMap = new Map();
        this.itemCounts = new Map();
        this.serverAddress = window.location.host;
        
        this.initializeElements();
        this.bindEvents();
        this.initializeItemCounts();
    }

    initializeElements() {
        // 连接相关
        this.statusEl = document.getElementById('status');
        this.connectBtn = document.getElementById('connectBtn');
        
        // 模式切换
        this.adminModeBtn = document.getElementById('adminModeBtn');
        this.gameModeBtn = document.getElementById('gameModeBtn');
        this.adminMode = document.getElementById('adminMode');
        this.gameMode = document.getElementById('gameMode');
        
        // 管理员模式元素
        this.statsOperation = document.getElementById('statsOperation');
        this.statsMapId = document.getElementById('statsMapId');
        this.statsKey = document.getElementById('statsKey');
        this.statsValue = document.getElementById('statsValue');
        this.statsSetGroup = document.getElementById('statsSetGroup');
        this.statsSubmit = document.getElementById('statsSubmit');
        this.moduleMode = document.getElementById('moduleMode');
        this.moduleSubmit = document.getElementById('moduleSubmit');
        this.cleanupSubmit = document.getElementById('cleanupSubmit');
        
        // 游戏模式元素
        this.usersEl = document.getElementById('users');
        this.requestsEl = document.getElementById('requests');
        this.apiTypeEl = document.getElementById('apiType');
        this.costEl = document.getElementById('cost');
        this.repeatEl = document.getElementById('repeat');
        this.teamEl = document.getElementById('team');
        this.jyEl = document.getElementById('jy');
        this.gameSubmit = document.getElementById('gameSubmit');
        
        // 结果显示
        this.outputEl = document.getElementById('output');
        this.clearOutputBtn = document.getElementById('clearOutput');
        this.verboseLogging = document.getElementById('verboseLogging');
        this.statsDisplay = document.getElementById('statsDisplay');
        this.itemsTableBody = document.getElementById('itemsTableBody');
    }

    bindEvents() {
        this.connectBtn.addEventListener('click', () => this.toggleConnection());
        
        // 模式切换
        this.adminModeBtn.addEventListener('click', () => this.switchMode('admin'));
        this.gameModeBtn.addEventListener('click', () => this.switchMode('game'));
        
        // 管理员模式事件
        this.statsOperation.addEventListener('change', () => this.toggleStatsSetGroup());
        this.statsSubmit.addEventListener('click', () => this.handleStatsRequest());
        this.moduleSubmit.addEventListener('click', () => this.handleModuleRequest());
        this.cleanupSubmit.addEventListener('click', () => this.handleCleanupRequest());
        
        // 游戏模式事件
        this.gameSubmit.addEventListener('click', () => this.handleGameRequest());
        
        // 其他事件
        this.clearOutputBtn.addEventListener('click', () => this.clearOutput());
    }

    initializeItemCounts() {
        this.itemCounts.set(100, [0, 0, 0, 0, 0, 0, 0, 0]);
        this.itemCounts.set(1000, [0, 0, 0, 0, 0, 0, 0, 0]);
        this.itemCounts.set(10000, [0, 0, 0, 0, 0, 0, 0, 0]);
    }

    toggleConnection() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.disconnect();
        } else {
            this.connect();
        }
    }

    connect() {
        try {
            const wsUrl = `ws://${this.serverAddress}/ws/991?v=1.0&timeout=1`;
            this.ws = new WebSocket(wsUrl);

            // 设置二进制数据类型为ArrayBuffer
            this.ws.binaryType = 'arraybuffer';

            this.ws.onopen = () => {
                this.updateStatus('已连接', 'connected');
                this.connectBtn.textContent = '断开';
                this.log('WebSocket连接已建立', 'success', true);
            };
            
            this.ws.onmessage = (event) => {
                this.handleMessage(event);
            };
            
            this.ws.onclose = () => {
                this.updateStatus('已断开', 'disconnected');
                this.connectBtn.textContent = '连接';
                this.log('WebSocket连接已关闭', 'warning');
            };
            
            this.ws.onerror = (error) => {
                this.log(`WebSocket错误: ${error}`, 'error');
            };
            
        } catch (error) {
            this.log(`连接失败: ${error.message}`, 'error');
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }

    updateStatus(text, className) {
        this.statusEl.textContent = text;
        this.statusEl.className = className;
    }

    switchMode(mode) {
        if (mode === 'admin') {
            this.adminMode.classList.remove('hidden');
            this.gameMode.classList.add('hidden');
            this.adminModeBtn.classList.add('active');
            this.gameModeBtn.classList.remove('active');
        } else {
            this.gameMode.classList.remove('hidden');
            this.adminMode.classList.add('hidden');
            this.gameModeBtn.classList.add('active');
            this.adminModeBtn.classList.remove('active');
        }
    }

    toggleStatsSetGroup() {
        const isSet = this.statsOperation.value === 'set';
        this.statsSetGroup.style.display = isSet ? 'block' : 'none';
    }

    async handleStatsRequest() {
        const operation = this.statsOperation.value;
        const mapId = this.statsMapId.value;
        
        if (!mapId) {
            this.log('请输入地图ID', 'error');
            return;
        }
        
        try {
            let url = `http://${this.serverAddress}/stats/global/${mapId}`;
            let options = { method: 'GET' };
            
            if (operation === 'set') {
                const key = this.statsKey.value;
                const value = this.statsValue.value;
                
                if (!key || !value) {
                    this.log('请输入键和值', 'error');
                    return;
                }
                
                url += `/${key}`;
                options = {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ value })
                };
            }
            
            const response = await fetch(url, options);
            const result = await response.text();
            this.log(`统计请求结果: ${result}`, 'info');
            
        } catch (error) {
            this.log(`统计请求失败: ${error.message}`, 'error');
        }
    }

    async handleModuleRequest() {
        const mode = this.moduleMode.value;
        
        try {
            let url = `http://${this.serverAddress}/api/modules`;
            
            if (mode === 'admin') {
                url += '?admin=true';
            } else if (mode === 'detailed') {
                url += '?admin=true&detailed=true';
            }
            
            const response = await fetch(url);
            const result = await response.json();
            this.log(`模块信息: ${JSON.stringify(result, null, 2)}`, 'info');
            
        } catch (error) {
            this.log(`模块查询失败: ${error.message}`, 'error');
        }
    }

    async handleCleanupRequest() {
        try {
            const response = await fetch(`http://${this.serverAddress}/stats/cleanup`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const result = await response.text();
            this.log(`清理结果: ${result}`, 'info');
            
        } catch (error) {
            this.log(`清理失败: ${error.message}`, 'error');
        }
    }

    handleGameRequest() {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            this.log('请先连接WebSocket', 'error');
            return;
        }
        
        const users = parseInt(this.usersEl.value);
        const requests = parseInt(this.requestsEl.value);
        const apiType = parseInt(this.apiTypeEl.value);
        const cost = parseInt(this.costEl.value);
        const repeat = parseInt(this.repeatEl.value);
        const team = parseInt(this.teamEl.value);
        const jy = parseInt(this.jyEl.value);
        
        this.log(`发送请求: 用户数=${users}, 请求数=${requests}, API=${apiType}, 成本=${cost}`, 'success', true);

        // 清空统计
        this.itemMap.clear();
        this.initializeItemCounts();

        const startTime = Date.now();
        
        // 生成用户列表
        const usersList = [];
        for (let i = 0; i < users; i++) {
            const uid = (100000 + i).toString();
            for (let j = 0; j < requests; j++) {
                usersList.push(uid);
            }
        }
        
        // 随机打乱
        for (let i = usersList.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [usersList[i], usersList[j]] = [usersList[j], usersList[i]];
        }
        
        // 发送请求
        usersList.forEach(uid => {
            this.sendGameMessage(apiType, uid, cost, repeat, team, jy);
        });
        
        const elapsed = Date.now() - startTime;
        this.log(`请求发送完成，耗时: ${elapsed}ms`, 'success', true);
    }

    sendGameMessage(api, userId, cost, repeat, team, jy) {
        const message = this.buildMessage(api, userId, cost, repeat, team, jy);
        if (message) {
            this.ws.send(message);
            this.flagIdx++;
        }
    }

    buildMessage(api, userId, cost, repeat, team, jy) {
        let jsonData;

        try {
            switch (api) {
                case 0:
                    // 心跳包，不需要JSON数据
                    return this.buildBinaryMessage(0, new Uint8Array(0));

                case 1:
                    // 普通怪
                    jsonData = JSON.stringify({
                        uid: userId,
                        cost: cost,
                        repeat: repeat,
                        team: team,
                        jy: jy
                    });
                    break;

                case 3:
                    // 世界BOSS
                    const users = {};
                    users["A"] = [];
                    users["B"] = [];
                    users["C"] = [];
                    users["D"] = [];
                    users["S"] = [];

                    for (let i = 0; i < repeat; i++) {
                        const uid = (100000 + i).toString();
                        const groups = ["A", "B", "C", "D"];
                        const randomGroup = groups[Math.floor(Math.random() * groups.length)];

                        const maxUsers = cost === 100 ? 100 : cost === 1000 ? 25 : 5;
                        if (users[randomGroup].length < maxUsers) {
                            users[randomGroup].push(uid);
                        }

                        if (team === 1 && users["S"].length < 1) {
                            users["S"].push(uid);
                        }
                    }

                    jsonData = JSON.stringify({
                        users: users,
                        cost: cost
                    });
                    break;

                case 4:
                    // 补充奖池
                    if (team === 1) {
                        jsonData = JSON.stringify({
                            mod: 1,
                            uid: userId,
                            cost: cost,
                            retn: repeat
                        });
                    } else {
                        jsonData = JSON.stringify({
                            cost: cost,
                            retn: repeat
                        });
                    }
                    break;

                case 5:
                    // 每日福利
                    jsonData = JSON.stringify({
                        uid: userId,
                        cost: cost,
                        games: repeat
                    });
                    break;

                case 6:
                    // 内丹修炼
                    jsonData = JSON.stringify({
                        uid: userId,
                        map: cost,
                        cost: cost * repeat,
                        multi: team
                    });
                    break;

                case 7:
                case 8:
                    // 排行榜
                    jsonData = JSON.stringify({
                        id: team
                    });
                    break;

                case 9:
                    // 宝物开奖
                    let counts;
                    let uid;

                    if (team === 1) {
                        // 个人开奖
                        const length = cost === 0 ? 24 : 8;
                        counts = new Array(length).fill(0);
                        let total = repeat;

                        for (let i = 0; i < length - 1; i++) {
                            counts[i] = Math.floor(Math.random() * (total + 1));
                            total -= counts[i];
                        }
                        counts[length - 1] = total;
                        uid = userId;
                    } else {
                        // 全局开奖
                        if (cost === 0) {
                            counts = new Array(24).fill(0);
                        } else {
                            counts = this.itemCounts.get(cost) || new Array(8).fill(0);
                        }
                        uid = "-1";
                    }

                    const oid = Math.floor(Math.random() * 90000) + 10000;
                    jsonData = JSON.stringify({
                        map: cost,
                        counts: counts,
                        oid: oid.toString(),
                        uid: uid
                    });
                    break;

                case 10:
                case 11:
                    // 哪吒救母 / 内丹探索
                    jsonData = JSON.stringify({
                        uid: userId,
                        cost: cost,
                        repeat: repeat,
                        jy: jy
                    });
                    break;

                case 12:
                    // 重抽
                    const rerollCounts = new Array(8).fill(0);
                    let rerollTotal = repeat;

                    for (let i = 0; i < 7; i++) {
                        rerollCounts[i] = Math.floor(Math.random() * (rerollTotal + 1));
                        rerollTotal -= rerollCounts[i];
                    }
                    rerollCounts[7] = rerollTotal;

                    const rerollOid = Math.floor(Math.random() * 90000) + 10000;
                    const rejackpot = repeat * cost;

                    jsonData = JSON.stringify({
                        map: cost,
                        counts: rerollCounts,
                        oid: rerollOid.toString(),
                        uid: userId,
                        rejackpot: rejackpot
                    });
                    break;

                case 20:
                    // 仙丹掉落
                    jsonData = JSON.stringify({
                        uid: userId,
                        cost: cost,
                        repeat: repeat,
                        jy: jy
                    });
                    break;

                case 21:
                    // 仙丹开鼎
                    const elixirCounts = new Array(8).fill(0);
                    let elixirTotal = repeat;

                    for (let i = 0; i < 7; i++) {
                        elixirCounts[i] = Math.floor(Math.random() * (elixirTotal + 1));
                        elixirTotal -= elixirCounts[i];
                    }
                    elixirCounts[7] = elixirTotal;

                    const elixirOid = Math.floor(Math.random() * 90000) + 10000;

                    jsonData = JSON.stringify({
                        map: cost,
                        counts: elixirCounts,
                        oid: elixirOid.toString(),
                        uid: userId
                    });
                    break;

                default:
                    jsonData = JSON.stringify({
                        uid: userId,
                        cost: cost,
                        repeat: repeat,
                        team: team,
                        jy: jy
                    });
            }

            // 构建二进制消息
            const jsonBytes = new TextEncoder().encode(jsonData);
            return this.buildBinaryMessage(api, jsonBytes);

        } catch (error) {
            this.log(`构建消息失败: ${error.message}`, 'error');
            return null;
        }
    }

    buildBinaryMessage(api, jsonBytes) {
        const buffer = new ArrayBuffer(14 + jsonBytes.length);
        const view = new DataView(buffer);

        // 消息长度 (大端序)
        view.setUint32(0, jsonBytes.length + 14, false);
        // 消息状态
        view.setUint8(4, api === 0 ? 6 : 0);
        // 协议类型
        view.setUint8(5, api);
        // 协议标识 (大端序)
        view.setBigUint64(6, BigInt(this.flagIdx), false);

        // 协议数据
        if (jsonBytes.length > 0) {
            const uint8Array = new Uint8Array(buffer);
            uint8Array.set(jsonBytes, 14);
        }

        this.log(`构建消息: 长度=${jsonBytes.length + 14}, API=${api}, 标识=${this.flagIdx}`, 'info');

        return buffer;
    }

    handleMessage(event) {
        // 处理WebSocket消息
        this.log(`收到消息，数据类型: ${typeof event.data}, 是否为ArrayBuffer: ${event.data instanceof ArrayBuffer}`, 'info');

        if (event.data instanceof ArrayBuffer) {
            const data = event.data;
            const view = new DataView(data);

            this.log(`ArrayBuffer长度: ${data.byteLength}`, 'info');

            // 解析消息头
            const msgLen = view.getUint32(0, false);
            const msgState = view.getUint8(4);
            const msgType = view.getUint8(5);
            const msgFlag = view.getBigUint64(6, false);

            this.log(`收到消息头: 长度=${msgLen}, 状态=${msgState}, 类型=${msgType}, 标识=${msgFlag}`, 'info');

            if (msgState !== 0 && msgState !== 5) {
                this.log(`消息状态错误: ${msgState}`, 'error', true);
                return;
            }

            // 提取消息体
            const msgBody = data.slice(14);
            const jsonStr = new TextDecoder().decode(msgBody);

            this.log(`收到完整响应 [类型:${msgType}]:\n${jsonStr}`, 'success', true);

            try {
                if (jsonStr.trim()) {
                    const response = JSON.parse(jsonStr);
                    this.log(`解析后的响应对象:`, 'info');
                    this.log(JSON.stringify(response, null, 2), 'info');
                    this.processResponse(msgType, response);
                } else {
                    this.log(`收到空响应体`, 'warning', true);
                }
            } catch (error) {
                this.log(`解析响应失败: ${error.message}`, 'error', true);
                this.log(`原始JSON字符串: ${jsonStr}`, 'error', true);
            }
        } else if (typeof event.data === 'string') {
            this.log(`收到文本消息: ${event.data}`, 'info', true);
        } else {
            this.log(`收到未知类型消息: ${typeof event.data}`, 'warning', true);
            this.log(`消息内容: ${JSON.stringify(event.data)}`, 'warning', true);
        }
    }

    processResponse(msgType, response) {
        if (response.code === 0 && response.data) {
            // 处理成功响应
            this.updateStats(msgType, response.data);
        }
    }

    updateStats(msgType, data) {
        // 根据消息类型处理不同的响应数据
        switch (msgType) {
            case 1:
            case 5:
            case 6:
            case 10:
            case 11:
            case 20:
                this.updateItemStats(data);
                break;
            case 3:
                this.updateBossStats(data);
                break;
            case 7:
                this.updateRankStats(data);
                break;
            case 9:
            case 12:
            case 21:
                this.updateExchangeStats(data);
                break;
        }

        this.displayStats();
    }

    updateItemStats(data) {
        if (data.items && Array.isArray(data.items)) {
            this.log(`更新物品统计: 成本=${data.cost}, 物品数量=${data.items.length}`, 'info');

            const giftMap = this.getGiftMap(data.cost);
            let counts = this.itemCounts.get(data.cost) || [0, 0, 0, 0, 0, 0, 0, 0];

            let totalCost = 0;
            let totalReturn = 0;

            for (const item of data.items) {
                const key = item.name;
                const itemTotal = item.total || 1;
                const itemValue = item.value || 0;
                const itemCount = item.count || 1;

                this.log(`处理物品: ${key}, 数量=${itemTotal}, 价值=${itemValue}, 倍数=${itemCount}`, 'info');

                if (!this.itemMap.has(key)) {
                    this.itemMap.set(key, {
                        name: key,
                        value: itemValue,
                        count: itemCount,
                        total: itemTotal
                    });
                } else {
                    const existing = this.itemMap.get(key);
                    existing.total += itemTotal;
                }

                // 更新计数
                const index = giftMap.indexOf(item.name);
                if (index >= 0) {
                    counts[index] += itemTotal;
                }

                // 计算成本和回报
                totalCost += (data.cost || 0) * (data.repeat || 1);
                totalReturn += itemValue * itemCount * itemTotal;
            }

            this.itemCounts.set(data.cost, counts);

            // 显示RTP信息
            if (totalCost > 0) {
                const rtp = (totalReturn / totalCost * 100).toFixed(2);
                this.log(`成本统计: 总成本=${totalCost}, 总回报=${totalReturn}, RTP=${rtp}%`, 'success', true);
            }
        }
    }

    updateBossStats(data) {
        if (data.items) {
            let totalCost = 0;
            let totalReturn = 0;

            for (const [group, items] of Object.entries(data.items)) {
                let groupValue = 0;
                for (const item of items) {
                    const key = item.name;
                    if (!this.itemMap.has(key)) {
                        this.itemMap.set(key, { ...item, total: 1 });
                    } else {
                        const existing = this.itemMap.get(key);
                        existing.total += 1;
                    }
                    groupValue += item.value;
                }

                const usersCost = (data.users[group] || []).length * data.cost;
                totalCost += usersCost;
                totalReturn += groupValue;

                this.log(`组 ${group}: 用户数=${(data.users[group] || []).length}, 总价值=${groupValue}, RTP=${((groupValue / usersCost) * 100).toFixed(2)}%`, 'info');
            }

            this.log(`Boss结果 -- 总成本: ${totalCost}, 总回报: ${totalReturn}, RTP: ${((totalReturn / totalCost) * 100).toFixed(2)}%`, 'success');
        }
    }

    updateRankStats(data) {
        if (data.items && Array.isArray(data.items)) {
            this.itemMap.clear();
            for (const item of data.items) {
                const key = item.name;
                this.itemMap.set(key, { ...item, total: item.total || 1 });
            }
        }
    }

    updateExchangeStats(data) {
        if (data.items && Array.isArray(data.items)) {
            let totalCost = 0;
            let totalReturn = 0;

            for (let i = 0; i < data.items.length; i++) {
                const item = data.items[i];
                const name = `[${i.toString().padStart(2, '0')}]${item.name}`;

                if (!this.itemMap.has(name)) {
                    this.itemMap.set(name, { ...item, total: item.total || 1 });
                } else {
                    const existing = this.itemMap.get(name);
                    existing.total += (item.total || 1);
                }

                let currentMap = data.map || 0;
                if (currentMap === 0) {
                    if (i < 8) currentMap = 100;
                    else if (i < 16) currentMap = 1000;
                    else currentMap = 10000;
                }

                totalCost += (item.total || 1) * currentMap;
                totalReturn += (item.total || 1) * item.value * (item.count || 1);
            }

            // 重置计数
            this.itemCounts.set(100, [0, 0, 0, 0, 0, 0, 0, 0]);
            this.itemCounts.set(1000, [0, 0, 0, 0, 0, 0, 0, 0]);
            this.itemCounts.set(10000, [0, 0, 0, 0, 0, 0, 0, 0]);

            this.log(`OID: ${data.oid || data.OID}, Map: ${data.map}, 总成本: ${totalCost}, 总回报: ${totalReturn}, RTP: ${((totalReturn / totalCost) * 100).toFixed(2)}%`, 'success');

            if (data.elixirs) {
                this.log(`开奖结果: ${JSON.stringify(data.elixirs)}`, 'info');
            }
        }
    }

    getGiftMap(cost) {
        const ITEM_MAP = {
            100: ["摄魂镜", "九瓣铜锤", "混天绫", "火尖枪", "羊脂玉净瓶", "幌金绳", "金铙", "人种袋"],
            1000: ["双股剑", "捣药杵", "九叶灵芝草", "迷魂帕", "倒马毒桩", "月牙铲", "佛宝舍利子", "白骨鞭"],
            10000: ["紫金铃", "四明铲", "随心铁杆兵", "紫金红葫芦", "七星剑", "乾坤圈", "阴阳二气瓶", "妖国令旗"]
        };
        return ITEM_MAP[cost] || [];
    }

    displayStats() {
        // 更新表格
        this.updateItemsTable();

        // 更新统计信息
        let statsText = '=== 统计信息 ===\n';

        if (this.itemMap.size > 0) {
            let totalCost = 0;
            let totalReturn = 0;
            let itemCount = 0;

            const sortedItems = Array.from(this.itemMap.entries()).sort();

            for (const [name, item] of sortedItems) {
                const itemTotalValue = (item.value || 0) * (item.count || 1) * (item.total || 1);
                totalReturn += itemTotalValue;
                itemCount += (item.total || 1);
                statsText += `${name}: 价值=${item.value || 0} 数量=${item.total || 1} 总价值=${itemTotalValue}\n`;
            }

            statsText += `\n总计: 道具种类=${this.itemMap.size}, 道具总数=${itemCount}, 总价值=${totalReturn}\n`;

            if (totalCost > 0) {
                const rtp = (totalReturn / totalCost * 100).toFixed(2);
                statsText += `RTP: ${rtp}%\n`;
            }
        } else {
            statsText += '暂无数据\n';
        }

        this.statsDisplay.textContent = statsText;
    }

    updateItemsTable() {
        // 清空表格
        this.itemsTableBody.innerHTML = '';

        if (this.itemMap.size === 0) {
            const row = document.createElement('tr');
            row.className = 'no-data';
            row.innerHTML = '<td colspan="4">暂无数据</td>';
            this.itemsTableBody.appendChild(row);
            return;
        }

        // 按名称排序
        const sortedItems = Array.from(this.itemMap.entries()).sort();

        for (const [name, item] of sortedItems) {
            const row = document.createElement('tr');
            const value = item.value || 0;
            const count = item.total || 1;
            const multiplier = item.count || 1;
            const totalValue = value * multiplier * count;

            row.innerHTML = `
                <td class="item-name">${name}</td>
                <td class="item-value">${value.toLocaleString()}</td>
                <td class="item-count">${count}</td>
                <td class="item-total">${totalValue.toLocaleString()}</td>
            `;

            this.itemsTableBody.appendChild(row);
        }
    }

    log(message, type = 'info', forceShow = false) {
        // 如果不是详细日志模式，只显示重要信息
        if (!forceShow && !this.verboseLogging.checked && (type === 'info' && !message.includes('收到完整响应'))) {
            return;
        }

        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}\n`;

        this.outputEl.textContent += logEntry;
        this.outputEl.scrollTop = this.outputEl.scrollHeight;

        // 控制台输出
        if (type === 'error') {
            console.error(message);
        } else if (type === 'warning') {
            console.warn(message);
        } else {
            console.log(message);
        }
    }

    clearOutput() {
        this.outputEl.textContent = '';
        this.statsDisplay.textContent = '';
        this.itemMap.clear();
        this.initializeItemCounts();
        this.updateItemsTable();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new JackpotTestTool();
});
