<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽奖系统测试工具</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>抽奖系统测试工具</h1>
            <div class="connection-status">
                <span id="status">未连接</span>
                <button id="connectBtn">连接</button>
            </div>
        </header>

        <div class="mode-selector">
            <button id="adminModeBtn" class="mode-btn">管理员模式</button>
            <button id="gameModeBtn" class="mode-btn">游戏接口模式</button>
        </div>

        <div class="main-content">
            <!-- 顶部面板 - 控制区域 -->
            <div class="top-panel">
                <!-- 管理员模式 -->
                <div id="adminMode" class="mode-panel hidden">
                    <h2>管理员模式</h2>
                    <div class="admin-section">
                        <h3>全局统计管理</h3>
                        <div class="form-group">
                            <label>操作类型:</label>
                            <select id="statsOperation">
                                <option value="get">获取统计</option>
                                <option value="set">设置统计</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>地图ID:</label>
                            <input type="number" id="statsMapId" placeholder="例如: 100, 1000, 10000">
                        </div>
                        <div class="form-group" id="statsSetGroup" style="display: none;">
                            <label>键:</label>
                            <input type="text" id="statsKey" placeholder="例如: rtp">
                            <label>值:</label>
                            <input type="text" id="statsValue" placeholder="例如: 0.95">
                        </div>
                        <button id="statsSubmit">执行</button>
                    </div>

                    <div class="admin-section">
                        <h3>模块信息</h3>
                        <div class="form-group">
                            <label>查询模式:</label>
                            <select id="moduleMode">
                                <option value="basic">基础信息</option>
                                <option value="admin">管理员模式</option>
                                <option value="detailed">详细模式</option>
                            </select>
                        </div>
                        <button id="moduleSubmit">查询模块</button>
                    </div>

                    <div class="admin-section">
                        <h3>数据清理</h3>
                        <button id="cleanupSubmit">清理统计数据</button>
                    </div>
                </div>

                <!-- 游戏接口模式 -->
                <div id="gameMode" class="mode-panel hidden">
                    <h2>游戏接口模式</h2>
                    <div class="game-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>用户数:</label>
                                <input type="number" id="users" value="1" min="1">
                            </div>
                            <div class="form-group">
                                <label>请求数:</label>
                                <input type="number" id="requests" value="1" min="1">
                            </div>
                            <div class="form-group">
                                <label>接口:</label>
                                <select id="apiType">
                                    <option value="1">普通怪 (1)</option>
                                    <option value="3">世界BOSS (3)</option>
                                    <option value="4">补充奖池 (4)</option>
                                    <option value="5">每日福利 (5)</option>
                                    <option value="6">内丹修炼 (6)</option>
                                    <option value="7">排行榜 (7)</option>
                                    <option value="8">查询排行榜 (8)</option>
                                    <option value="9">宝物开奖 (9)</option>
                                    <option value="10">哪吒救母 (10)</option>
                                    <option value="11">内丹探索 (11)</option>
                                    <option value="12">重抽 (12)</option>
                                    <option value="20">仙丹掉落 (20)</option>
                                    <option value="21">仙丹开鼎 (21)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>成本:</label>
                                <select id="cost">
                                    <option value="100">100</option>
                                    <option value="1000">1000</option>
                                    <option value="10000">10000</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>连击:</label>
                                <input type="number" id="repeat" value="1" min="1">
                            </div>
                            <div class="form-group">
                                <label>队伍:</label>
                                <input type="number" id="team" value="0" min="0">
                            </div>
                            <div class="form-group">
                                <label>精英:</label>
                                <input type="number" id="jy" value="0" min="0">
                            </div>
                        </div>
                        <button id="gameSubmit">发送请求</button>
                    </div>
                </div>
            </div>

            <!-- 中间面板 - 结果和道具列表 -->
            <div class="middle-panel">
                <!-- 结果显示区域 -->
                <div class="results-panel">
                    <div class="results">
                        <h3>结果</h3>
                        <div class="result-controls">
                            <label>
                                <input type="checkbox" id="verboseLogging">
                                详细日志
                            </label>
                            <button id="clearOutput">清空结果</button>
                        </div>
                        <div id="output" class="output-area"></div>
                    </div>
                </div>

                <!-- 道具列表 -->
                <div class="items-panel-wrapper">
                    <div class="items-panel">
                        <h3>道具列表</h3>
                        <div class="table-container">
                            <table id="itemsTable" class="items-table">
                                <colgroup>
                                    <col style="width: 40%;">
                                    <col style="width: 20%;">
                                    <col style="width: 20%;">
                                    <col style="width: 20%;">
                                </colgroup>
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>单价/价值</th>
                                        <th>次数</th>
                                        <th>总值</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody">
                                    <tr class="no-data">
                                        <td colspan="4">暂无数据</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
