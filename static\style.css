* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 100%;
    margin: 0;
    padding: 10px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

header h1 {
    font-size: 2rem;
    font-weight: 300;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

#status {
    padding: 5px 10px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.2);
    font-size: 0.9rem;
}

#status.connected {
    background: #4CAF50;
}

#status.disconnected {
    background: #f44336;
}

button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s;
}

button:hover {
    background: #45a049;
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    overflow: hidden;
}

.top-panel {
    flex-shrink: 0;
}

.middle-panel {
    display: flex;
    gap: 15px;
    flex: 1;
    overflow: hidden;
}

.results-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.items-panel-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}



.mode-selector {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    flex-shrink: 0;
}

.mode-btn {
    flex: 1;
    padding: 15px;
    font-size: 1.1rem;
    background: #e0e0e0;
    color: #333;
}

.mode-btn.active {
    background: #2196F3;
    color: white;
}

.mode-panel {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    overflow-y: auto;
}

.hidden {
    display: none;
}

.admin-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.admin-section h3 {
    margin-bottom: 15px;
    color: #2196F3;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 10px;
}

.game-form {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
}

.game-form .form-row:last-of-type {
    margin-bottom: 10px;
}

.results {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.result-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.result-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.output-area {
    background: #1e1e1e;
    color: #00ff00;
    padding: 10px;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    flex: 1;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 日志颜色样式 */
.log-error {
    color: #ff4444 !important;
}

.log-warning {
    color: #ffaa00 !important;
}

.log-info {
    color: #00aaff !important;
}

.log-success {
    color: #00ff88 !important;
}

.log-default {
    color: #00ff00 !important;
}

.items-panel {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.table-container {
    overflow: auto;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    flex: 1;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    background: #1a1a1a;
    color: #ffffff;
    table-layout: fixed;
}

.items-table col:nth-child(1) { width: 40%; }
.items-table col:nth-child(2) { width: 20%; }
.items-table col:nth-child(3) { width: 20%; }
.items-table col:nth-child(4) { width: 20%; }

.items-table thead {
    background: #2d2d2d;
}

.items-table th {
    padding: 12px 15px;
    font-weight: 600;
    border-bottom: 2px solid #404040;
    color: #ffffff;
}

.items-table th:first-child {
    text-align: left;
}

.items-table th:not(:first-child) {
    text-align: right;
}

.items-table td {
    padding: 10px 15px;
    border-bottom: 1px solid #404040;
}

.items-table tbody tr:hover {
    background: #2a2a2a;
}

.items-table tbody tr:nth-child(even) {
    background: #1f1f1f;
}

.items-table tbody tr:nth-child(even):hover {
    background: #2a2a2a;
}

.items-table .no-data {
    text-align: center;
    color: #888;
    font-style: italic;
}

.items-table .item-name {
    font-weight: 500;
    color: #4CAF50;
    text-align: left;
}

.items-table .item-value {
    color: #FFC107;
    text-align: right;
    font-family: 'Courier New', monospace;
}

.items-table .item-count {
    color: #2196F3;
    text-align: right;
    font-family: 'Courier New', monospace;
}

.items-table .item-total {
    color: #FF5722;
    text-align: right;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}



.error {
    color: #f44336;
}

.success {
    color: #4CAF50;
}

.warning {
    color: #ff9800;
}

.info {
    color: #2196F3;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .middle-panel {
        flex-direction: column;
    }

    .results-panel, .items-panel-wrapper {
        flex: none;
        margin-bottom: 10px;
    }

    .container {
        height: auto;
        overflow: visible;
    }

    .results {
        max-height: 300px;
    }

    .output-area {
        max-height: 250px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .mode-selector {
        flex-direction: column;
    }

    .middle-panel {
        flex-direction: column;
    }

    .results-panel, .items-panel-wrapper {
        margin-bottom: 10px;
    }
}
